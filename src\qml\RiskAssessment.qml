import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Controls.Material 2.15
import QtQuick.Layouts 1.15
import QtQuick.Dialogs

pragma ComponentBehavior: Bound

ScrollView {
    id: root
    anchors.fill: parent
    contentWidth: availableWidth

    // 风险评估引擎引用
    property var riskEngine: null

    // 当前评估数据
    property var currentAssessment: riskEngine ? riskEngine.currentAssessment : {}
    property double totalRiskScore: riskEngine ? riskEngine.totalRiskScore : 0.0
    property string riskLevel: riskEngine ? riskEngine.riskLevel : "无风险"
    property var riskLevelCounts: currentAssessment.riskLevelCounts || {}
    property var dataTypeRisks: currentAssessment.dataTypeRisks || {}
    property var riskTrends: riskEngine ? riskEngine.getRiskTrends() : []

    // 基于规则的风险评分相关属性
    property string selectedDataType: "所有数据资产"
    property var filteredRiskData: []
    property var customRiskRules: ({})
    property bool isCalculatingRisk: false

    // 风险等级颜色映射
    function getRiskColor(level) {
        switch(level) {
            case "高风险": return "#e74c3c"
            case "中风险": return "#f39c12"
            case "低风险": return "#f1c40f"
            case "无风险": return "#27ae60"
            default: return "#95a5a6"
        }
    }

    // 格式化风险评分显示
    function formatRiskScore(score) {
        return Math.round(score).toString()
    }

    // 计算风险分布百分比
    function calculateRiskPercentage(count, total) {
        return total > 0 ? (count / total) : 0
    }

    // 基于规则的风险评分相关函数
    function updateFilteredRiskData() {
        console.log("[RiskAssessment] 更新过滤的风险数据，选择的数据类型:", selectedDataType)

        if (!riskEngine) {
            console.log("[RiskAssessment] 风险引擎未设置")
            filteredRiskData = []
            updateRiskListModel()
            return
        }

        // 使用RiskAssessmentEngine的新方法获取过滤数据
        var newFilteredData = riskEngine.getFilteredRiskData(selectedDataType)

        console.log("[RiskAssessment] 从引擎获取的过滤数据数量:", newFilteredData.length)

        filteredRiskData = newFilteredData

        // 更新ListView模型
        updateRiskListModel()
    }

    function updateRiskListModel() {
        console.log("[RiskAssessment] 更新ListView模型，数据数量:", filteredRiskData.length)

        if (!riskListView || !riskListView.model) {
            console.log("[RiskAssessment] ListView或模型未准备好")
            return
        }

        try {
            riskListView.model.clear()

            for (var i = 0; i < filteredRiskData.length; i++) {
                var item = filteredRiskData[i]
                console.log("[RiskAssessment] 添加项目:", item.name, "评分:", item.score, "等级:", item.level)

                riskListView.model.append({
                    name: item.name || "未知文件",
                    type: item.type || "其他",
                    score: item.score || 0,
                    level: item.level || "未知",
                    filePath: item.filePath || "",
                    sensitiveItemCount: item.sensitiveItemCount || 0
                })
            }

            console.log("[RiskAssessment] ListView模型更新完成，当前项目数:", riskListView.model.count)
        } catch (error) {
            console.log("[RiskAssessment] 更新ListView模型时发生错误:", error)
        }
    }

    function recalculateRisk() {
        console.log("[RiskAssessment] 开始重新计算风险")

        if (!riskEngine) {
            console.log("[RiskAssessment] 风险引擎未设置")
            return
        }

        isCalculatingRisk = true
        riskEngine.refreshAssessment()
    }

    function showCustomRulesDialog() {
        console.log("[RiskAssessment] 显示自定义评分规则对话框")
        customRulesDialog.open()
        console.log("[RiskAssessment] 自定义评分规则对话框已打开")
    }

    function showRiskDetails(filePath, riskScore, riskLevel, sensitiveItemCount) {
        console.log("[RiskAssessment] 显示风险详情:", filePath, "评分:", riskScore, "等级:", riskLevel, "敏感项:", sensitiveItemCount)

        if (!riskEngine) {
            console.log("[RiskAssessment] 风险引擎未设置")
            return
        }

        // 获取详细的风险信息
        var details = riskEngine.getRiskDetailsForFile(filePath)
        console.log("[RiskAssessment] 获取到的风险详情:", JSON.stringify(details))

        if (details.error) {
            console.log("[RiskAssessment] 获取风险详情失败:", details.error)
            return
        }

        // 设置对话框数据并显示
        riskDetailsDialog.riskDetails = details
        riskDetailsDialog.open()

        console.log("[RiskAssessment] 风险详情对话框已打开")
    }

    function showMitigationMeasures(filePath, riskLevel) {
        console.log("[RiskAssessment] 显示缓解措施:", filePath, "风险等级:", riskLevel)

        if (!riskEngine) {
            console.log("[RiskAssessment] 风险引擎未设置")
            return
        }

        // 获取缓解措施
        var measures = riskEngine.getMitigationMeasuresForRisk(riskLevel)
        console.log("[RiskAssessment] 获取到的缓解措施数量:", measures.length)

        // 设置对话框数据并显示
        mitigationDialog.measures = measures
        mitigationDialog.riskLevel = riskLevel
        mitigationDialog.open()

        console.log("[RiskAssessment] 缓解措施对话框已打开")
    }

    // 监听风险引擎变化
    onRiskEngineChanged: {
        console.log("[RiskAssessment] 风险引擎已设置")
        if (riskEngine) {
            // 连接信号
            riskEngine.currentAssessmentChanged.connect(function() {
                console.log("[RiskAssessment] 收到评估结果变更信号")
                updateFilteredRiskData()
            })

            riskEngine.assessmentCompleted.connect(function() {
                console.log("[RiskAssessment] 收到评估完成信号")
                isCalculatingRisk = false
                updateFilteredRiskData()
            })

            riskEngine.assessmentError.connect(function(error) {
                console.log("[RiskAssessment] 收到评估错误信号:", error)
                isCalculatingRisk = false
            })

            // 初始化数据
            updateFilteredRiskData()
        }
    }

    // 监听选择的数据类型变化
    onSelectedDataTypeChanged: {
        console.log("[RiskAssessment] 数据类型选择变更:", selectedDataType)
        updateFilteredRiskData()
    }

    // 组件完成时初始化
    Component.onCompleted: {
        console.log("[RiskAssessment] 组件初始化完成")
        // 延迟一点时间确保所有组件都已准备好
        Qt.callLater(function() {
            if (riskEngine) {
                console.log("[RiskAssessment] 初始化时风险引擎已设置，开始更新数据")
                updateFilteredRiskData()
            } else {
                console.log("[RiskAssessment] 初始化时风险引擎未设置")
            }
        })
    }

    ColumnLayout {
        width: parent.width
        anchors.margins: 20
        spacing: 20
        clip: true

        // 页面标题
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: 60
            color: Qt.color("white")
            radius: 8
            border.color: Qt.color("#ddd")

            RowLayout {
                anchors.fill: parent
                anchors.margins: 8

                Text {
                    text: "⚠️ 风险评估与分析"
                    font.pixelSize: 24
                    font.bold: true
                    color: Qt.color("#2c3e50")
                }

                Item {
                    Layout.fillWidth: true
                }

                Button {
                    text: "生成风险报告"
                    Material.background: Qt.color("#e74c3c")
                    Material.foreground: Qt.color("white")
                    onClicked:
                    // 生成风险报告功能
                    {}
                }
            }
        }

        // 风险概览仪表板
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: Math.max(160, riskOverview.height + 32)
            color: Qt.color("white")
            radius: 8
            border.color: Qt.color("#ddd")

            RowLayout {
                id: riskOverview
                anchors.fill: parent
                anchors.margins: 16
                spacing: 20

                // 总体风险评分
                Rectangle {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: Qt.color("#f8f9fa")
                    radius: 6

                    ColumnLayout {
                        anchors.fill: parent
                        anchors.margins: 0
                        spacing: 8

                        Text {
                            text: "总体风险评分"
                            font.pixelSize: 16
                            font.bold: true
                            color: Qt.color("#2c3e50")
                        }

                        Rectangle {
                            Layout.preferredWidth: 80
                            Layout.preferredHeight: 80
                            radius: 50
                            color: Qt.color(root.getRiskColor(root.riskLevel))
                            Layout.alignment: Qt.AlignHCenter

                            Text {
                                anchors.centerIn: parent
                                text: root.formatRiskScore(root.totalRiskScore)
                                font.pixelSize: 32
                                font.bold: true
                                color: Qt.color("white")
                            }
                        }

                        Text {
                            text: root.riskLevel
                            color: Qt.color(root.getRiskColor(root.riskLevel))
                            font.bold: true
                            font.pixelSize: 14
                            Layout.alignment: Qt.AlignHCenter
                        }
                    }
                }

                // 风险分布
                Rectangle {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: Qt.color("#f8f9fa")
                    radius: 6

                    ColumnLayout {
                        anchors.fill: parent
                        anchors.margins: 0

                        Text {
                            text: "风险分布"
                            font.pixelSize: 16
                            font.bold: true
                            color: Qt.color("#2c3e50")
                        }

                        // 风险分布条形图
                        ColumnLayout {
                            Layout.fillWidth: true
                            Layout.fillHeight: true

                            // 高风险
                            RowLayout {
                                id: highRiskRow
                                Layout.fillWidth: true
                                property int highCount: root.riskLevelCounts.high || 0
                                property int totalCount: root.riskLevelCounts.total || 1
                                property double percentage: root.calculateRiskPercentage(highCount, totalCount)

                                Text {
                                    text: "高风险"
                                    Layout.preferredWidth: 60
                                    font.pixelSize: 12
                                    color: Qt.color("#2c3e50")
                                }
                                Rectangle {
                                    Layout.fillWidth: true
                                    Layout.preferredHeight: 16
                                    color: Qt.color("#f1f1f1")
                                    radius: 2

                                    Rectangle {
                                        width: parent.width * highRiskRow.percentage
                                        height: parent.height
                                        color: Qt.color("#e74c3c")
                                        radius: 2
                                    }
                                }
                                Text {
                                    text: highRiskRow.highCount + " (" + Math.round(highRiskRow.percentage * 100) + "%)"
                                    Layout.preferredWidth: 80
                                    font.pixelSize: 11
                                    color: Qt.color("#7f8c8d")
                                }
                            }

                            // 中风险
                            RowLayout {
                                id: mediumRiskRow
                                Layout.fillWidth: true
                                property int mediumCount: root.riskLevelCounts.medium || 0
                                property int totalCount: root.riskLevelCounts.total || 1
                                property double percentage: root.calculateRiskPercentage(mediumCount, totalCount)

                                Text {
                                    text: "中风险"
                                    Layout.preferredWidth: 60
                                    font.pixelSize: 12
                                    color: Qt.color("#2c3e50")
                                }
                                Rectangle {
                                    Layout.fillWidth: true
                                    Layout.preferredHeight: 16
                                    color: Qt.color("#f1f1f1")
                                    radius: 2

                                    Rectangle {
                                        width: parent.width * mediumRiskRow.percentage
                                        height: parent.height
                                        color: Qt.color("#f39c12")
                                        radius: 2
                                    }
                                }
                                Text {
                                    text: mediumRiskRow.mediumCount + " (" + Math.round(mediumRiskRow.percentage * 100) + "%)"
                                    Layout.preferredWidth: 80
                                    font.pixelSize: 11
                                    color: Qt.color("#7f8c8d")
                                }
                            }

                            // 低风险
                            RowLayout {
                                id: lowRiskRow
                                Layout.fillWidth: true
                                property int lowCount: root.riskLevelCounts.low || 0
                                property int totalCount: root.riskLevelCounts.total || 1
                                property double percentage: root.calculateRiskPercentage(lowCount, totalCount)

                                Text {
                                    text: "低风险"
                                    Layout.preferredWidth: 60
                                    font.pixelSize: 12
                                    color: Qt.color("#2c3e50")
                                }
                                Rectangle {
                                    Layout.fillWidth: true
                                    Layout.preferredHeight: 16
                                    color: Qt.color("#f1f1f1")
                                    radius: 2

                                    Rectangle {
                                        width: parent.width * lowRiskRow.percentage
                                        height: parent.height
                                        color: Qt.color("#27ae60")
                                        radius: 2
                                    }
                                }
                                Text {
                                    text: lowRiskRow.lowCount + " (" + Math.round(lowRiskRow.percentage * 100) + "%)"
                                    Layout.preferredWidth: 80
                                    font.pixelSize: 11
                                    color: Qt.color("#7f8c8d")
                                }
                            }
                        }
                    }
                }

                // 风险趋势
                Rectangle {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    color: Qt.color("#f8f9fa")
                    radius: 6

                    ColumnLayout {
                        anchors.fill: parent
                        anchors.margins: 0
                        spacing: 8

                        Text {
                            text: "风险趋势"
                            font.pixelSize: 16
                            font.bold: true
                            color: Qt.color("#2c3e50")
                        }

                        // 简单的折线图区域
                        Rectangle {
                            Layout.fillWidth: true
                            Layout.fillHeight: true
                            color: Qt.color("#ffffff")
                            radius: 4

                            Canvas {
                                id: trendChart
                                anchors.fill: parent
                                anchors.margins: 5

                                // 监听风险趋势数据变化
                                property var trendData: root.riskTrends

                                onTrendDataChanged: {
                                    requestPaint()
                                }

                                onPaint: {
                                    var ctx = getContext("2d");
                                    ctx.clearRect(0, 0, width, height);

                                    if (!trendData || trendData.length === 0) {
                                        // 显示无数据提示
                                        ctx.fillStyle = "#95a5a6";
                                        ctx.font = "14px Arial";
                                        ctx.textAlign = "center";
                                        ctx.fillText("暂无历史数据", width / 2, height / 2);
                                        return;
                                    }

                                    // 准备数据点
                                    var points = [];
                                    var maxScore = 100; // 最大风险评分
                                    var dataCount = Math.min(trendData.length, 7); // 最多显示7个数据点

                                    for (var i = 0; i < dataCount; i++) {
                                        var dataIndex = trendData.length - dataCount + i; // 从最新的数据开始
                                        if (dataIndex >= 0) {
                                            var item = trendData[dataIndex];
                                            var score = item.score || 0;
                                            var x = (width / (dataCount - 1)) * i;
                                            var y = height - (height * (score / maxScore));
                                            points.push({x: x, y: y, score: score});
                                        }
                                    }

                                    if (points.length < 2) {
                                        // 数据点不足，显示提示
                                        ctx.fillStyle = "#95a5a6";
                                        ctx.font = "12px Arial";
                                        ctx.textAlign = "center";
                                        ctx.fillText("需要更多历史数据", width / 2, height / 2);
                                        return;
                                    }

                                    // 绘制折线
                                    ctx.strokeStyle = "#3498db";
                                    ctx.lineWidth = 2;
                                    ctx.beginPath();
                                    ctx.moveTo(points[0].x, points[0].y);

                                    for (var j = 1; j < points.length; j++) {
                                        ctx.lineTo(points[j].x, points[j].y);
                                    }
                                    ctx.stroke();

                                    // 绘制数据点
                                    ctx.fillStyle = "#3498db";
                                    for (var k = 0; k < points.length; k++) {
                                        ctx.beginPath();
                                        ctx.arc(points[k].x, points[k].y, 3, 0, 2 * Math.PI);
                                        ctx.fill();
                                    }
                                }

                                Component.onCompleted: requestPaint()
                            }

                            // 显示最新评估时间
                            Text {
                                anchors.bottom: parent.bottom
                                anchors.right: parent.right
                                anchors.margins: 8
                                text: root.currentAssessment.assessmentTime ?
                                      "最后更新: " + Qt.formatDateTime(root.currentAssessment.assessmentTime, "MM-dd hh:mm") : ""
                                font.pixelSize: 10
                                color: Qt.color("#7f8c8d")
                            }
                        }
                    }
                }
            }
        }

        // 基于规则的风险评分区域
        GroupBox {
            Layout.fillWidth: true
            title: "基于规则的风险评分"

            ColumnLayout {
                anchors.fill: parent
                spacing: 12

                RowLayout {
                    Layout.fillWidth: true

                    Text {
                        text: "数据类型筛选:"
                        font.bold: true
                        verticalAlignment: Text.AlignVCenter
                    }

                    ComboBox {
                        id: dataTypeComboBox
                        Layout.preferredWidth: 200
                        model: ["所有数据资产", "PII数据", "财务数据", "健康数据", "商业机密"]
                        currentIndex: 0
                        onCurrentTextChanged: {
                            console.log("[RiskAssessment] ComboBox选择变更:", currentText)
                            selectedDataType = currentText
                        }
                    }

                    Button {
                        text: "自定义评分规则"
                        onClicked: {
                            console.log("[RiskAssessment] 点击自定义评分规则按钮")
                            showCustomRulesDialog()
                        }
                    }

                    Button {
                        text: isCalculatingRisk ? "计算中..." : "重新计算风险"
                        enabled: !isCalculatingRisk && riskEngine
                        Material.background: Qt.color("#3498db")
                        Material.foreground: Qt.color("white")
                        onClicked: {
                            console.log("[RiskAssessment] 点击重新计算风险按钮")
                            recalculateRisk()
                        }
                    }

                    // 显示当前过滤结果统计
                    Text {
                        text: "显示: " + (filteredRiskData ? filteredRiskData.length : 0) + " 项"
                        color: Qt.color("#7f8c8d")
                        font.pixelSize: 12
                        verticalAlignment: Text.AlignVCenter
                        Layout.leftMargin: 10
                    }
                }

                // 风险评分表格
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 200
                    border.color: Qt.color("#ddd")
                    radius: 4

                    ListView {
                        id: riskListView
                        anchors.fill: parent
                        anchors.margins: 8
                        clip: true
                        model: ListModel {
                            id: riskListModel
                            // 动态数据将通过updateRiskListModel()函数填充
                        }

                        header: Rectangle {
                            width: parent.width
                            height: 30
                            color: Qt.color("#f1f1f1")

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 8
                                spacing: 0

                                Text {
                                    text: "数据资产"
                                    font.bold: true
                                    Layout.preferredWidth: 150
                                }
                                Text {
                                    text: "数据类型"
                                    font.bold: true
                                    Layout.preferredWidth: 100
                                    horizontalAlignment: Text.AlignHCenter
                                }
                                Text {
                                    text: "风险评分"
                                    font.bold: true
                                    Layout.preferredWidth: 100
                                    horizontalAlignment: Text.AlignHCenter
                                }
                                Text {
                                    text: "风险级别"
                                    font.bold: true
                                    Layout.preferredWidth: 100
                                    horizontalAlignment: Text.AlignHCenter
                                }
                                Text {
                                    text: "操作"
                                    font.bold: true
                                    Layout.preferredWidth: 160
                                    horizontalAlignment: Text.AlignHCenter
                                }
                            }
                        }

                        delegate: Rectangle {
                            id: riskScoreDelegateItem
                            required property int index
                            required property var model
                            width: riskListView.width
                            height: 45
                            color: index % 2 === 0 ? Qt.color("#f8f9fa") : Qt.color("white")

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 0
                                spacing: 0  // 设置为0避免额外间距

                                Text {
                                    text: riskScoreDelegateItem.model.name
                                    Layout.preferredWidth: 150  // 与表头完全一致
                                    verticalAlignment: Text.AlignVCenter
                                }
                                Text {
                                    text: riskScoreDelegateItem.model.type
                                    Layout.preferredWidth: 100
                                    horizontalAlignment: Text.AlignHCenter
                                    verticalAlignment: Text.AlignVCenter
                                }
                                Text {
                                    text: riskScoreDelegateItem.model.score
                                    Layout.preferredWidth: 100
                                    horizontalAlignment: Text.AlignHCenter
                                    verticalAlignment: Text.AlignVCenter
                                    color: riskScoreDelegateItem.model.score > 70 ? Qt.color("#e74c3c") : (riskScoreDelegateItem.model.score > 40 ? Qt.color("#f39c12") : Qt.color("#2ecc71"))
                                }
                                Text {
                                    text: riskScoreDelegateItem.model.level
                                    Layout.preferredWidth: 100
                                    horizontalAlignment: Text.AlignHCenter
                                    verticalAlignment: Text.AlignVCenter
                                    color: riskScoreDelegateItem.model.score > 70 ? Qt.color("#e74c3c") : (riskScoreDelegateItem.model.score > 40 ? Qt.color("#f39c12") : Qt.color("#2ecc71"))
                                }
                                RowLayout {
                                    Layout.preferredWidth: 160
                                    //垂直居中
                                    Layout.alignment: Qt.AlignVCenter
                                    spacing: 4

                                    Button {
                                        text: "详情"
                                        flat: true
                                        Material.foreground: Qt.color("#3498db")
                                        onClicked: {
                                            console.log("[RiskAssessment] 点击详情按钮:", riskScoreDelegateItem.model.name)
                                            root.showRiskDetails(
                                                riskScoreDelegateItem.model.filePath || "",
                                                riskScoreDelegateItem.model.score || 0,
                                                riskScoreDelegateItem.model.level || "",
                                                riskScoreDelegateItem.model.sensitiveItemCount || 0
                                            )
                                        }
                                    }

                                    Button {
                                        text: "缓解措施"
                                        flat: true
                                        Material.foreground: Qt.color("#3498db")
                                        onClicked: {
                                            console.log("[RiskAssessment] 点击缓解措施按钮:", riskScoreDelegateItem.model.name)
                                            root.showMitigationMeasures(
                                                riskScoreDelegateItem.model.filePath || "",
                                                riskScoreDelegateItem.model.level || ""
                                            )
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        // 合规性检查区域
        GroupBox {
            Layout.fillWidth: true
            title: "合规性检查"

            ColumnLayout {
                anchors.fill: parent
                spacing: 12

                RowLayout {
                    Layout.fillWidth: true

                    Text {
                        text: "选择法规标准:"
                        font.bold: true
                    }

                    ComboBox {
                        Layout.preferredWidth: 200
                        model: ["GDPR", "CCPA", "PIPL", "HIPAA", "PCI DSS", "自定义标准"]
                        currentIndex: 2
                    }

                    Button {
                        text: "开始合规检查"
                        Material.background: Qt.color("#9b59b6")
                        Material.foreground: Qt.color("white")
                        onClicked:
                        // 开始合规检查
                        {}
                    }
                }

                // 合规检查结果
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 200
                    border.color: Qt.color("#ddd")
                    radius: 4
                    color: Qt.color("#f8f9fa")

                    ColumnLayout {
                        id: complianceResult
                        anchors.fill: parent
                        anchors.margins: 16
                        spacing: 12
                        clip: true

                        Text {
                            text: "PIPL 合规检查结果"
                            font.bold: true
                            font.pixelSize: 16
                        }

                        RowLayout {
                            Layout.fillWidth: true

                            Rectangle {
                                Layout.preferredWidth: 120
                                Layout.preferredHeight: 120
                                radius: 60
                                color: Qt.color("#f39c12")

                                Text {
                                    anchors.centerIn: parent
                                    text: "68%"
                                    font.pixelSize: 24
                                    font.bold: true
                                    color: Qt.color("white")
                                }
                            }

                            ColumnLayout {
                                Layout.fillWidth: true
                                spacing: 8

                                Text {
                                    text: "✅ 数据收集合规: 已实施"
                                    color: Qt.color("#27ae60")
                                }
                                Text {
                                    text: "✅ 数据存储安全: 已实施"
                                    color: Qt.color("#27ae60")
                                }
                                Text {
                                    text: "❌ 数据主体权利: 未完全实施"
                                    color: Qt.color("#e74c3c")
                                }
                                Text {
                                    text: "❌ 跨境数据传输: 不合规"
                                    color: Qt.color("#e74c3c")
                                }
                                Text {
                                    text: "⚠️ 数据处理记录: 部分合规"
                                    color: Qt.color("#f39c12")
                                }
                            }
                        }
                    }
                }
            }
        }

        Item {
            Layout.preferredHeight: 20
        }
    }
    // 风险详情对话框
    Dialog {
        id: riskDetailsDialog
        title: "风险详情"
        width: Math.min(600, parent.width * 0.9)
        height: Math.min(500, parent.height * 0.8)
        modal: true
        anchors.centerIn: parent

        property var riskDetails: ({})

        ScrollView {
            anchors.fill: parent
            anchors.margins: 10
            clip: true
            ScrollBar.horizontal.policy: ScrollBar.AsNeeded
            ScrollBar.vertical.policy: ScrollBar.AsNeeded

            ColumnLayout {
                width: parent.width - 20
                spacing: 15

                // 文件基本信息
                GroupBox {
                    Layout.fillWidth: true
                    title: "文件信息"

                    ColumnLayout {
                        anchors.fill: parent
                        spacing: 8

                        // 文件名
                        RowLayout {
                            Layout.fillWidth: true
                            Text {
                                text: "文件名:"
                                font.bold: true
                                Layout.minimumWidth: 60
                                Layout.maximumWidth: 80
                            }
                            Text {
                                text: riskDetailsDialog.riskDetails.fileName || "未知"
                                Layout.fillWidth: true
                                wrapMode: Text.WordWrap
                            }
                        }

                        // 文件路径
                        RowLayout {
                            Layout.fillWidth: true
                            Text {
                                text: "路径:"
                                font.bold: true
                                Layout.minimumWidth: 60
                                Layout.maximumWidth: 80
                            }
                            Text {
                                text: riskDetailsDialog.riskDetails.filePath || "未知"
                                Layout.fillWidth: true
                                wrapMode: Text.WordWrap
                            }
                        }

                        // 文件大小
                        RowLayout {
                            Layout.fillWidth: true
                            Text {
                                text: "大小:"
                                font.bold: true
                                Layout.minimumWidth: 60
                                Layout.maximumWidth: 80
                            }
                            Text {
                                text: (riskDetailsDialog.riskDetails.fileSize || 0) + " 字节"
                                Layout.fillWidth: true
                            }
                        }

                        // 最后修改时间
                        RowLayout {
                            Layout.fillWidth: true
                            Text {
                                text: "修改:"
                                font.bold: true
                                Layout.minimumWidth: 60
                                Layout.maximumWidth: 80
                            }
                            Text {
                                text: riskDetailsDialog.riskDetails.lastModified ?
                                      Qt.formatDateTime(riskDetailsDialog.riskDetails.lastModified, "yyyy-MM-dd hh:mm") : "未知"
                                Layout.fillWidth: true
                            }
                        }

                        // 数据类型
                        RowLayout {
                            Layout.fillWidth: true
                            Text {
                                text: "类型:"
                                font.bold: true
                                Layout.minimumWidth: 60
                                Layout.maximumWidth: 80
                            }
                            Text {
                                text: riskDetailsDialog.riskDetails.dataType || "未知"
                                Layout.fillWidth: true
                            }
                        }
                    }
                }

                // 风险评估信息
                GroupBox {
                    Layout.fillWidth: true
                    title: "风险评估"

                    ColumnLayout {
                        anchors.fill: parent
                        spacing: 8

                        // 风险评分
                        RowLayout {
                            Layout.fillWidth: true
                            Text {
                                text: "评分:"
                                font.bold: true
                                Layout.minimumWidth: 60
                                Layout.maximumWidth: 80
                            }
                            Text {
                                text: Math.round(riskDetailsDialog.riskDetails.riskScore || 0).toString()
                                font.bold: true
                                Layout.fillWidth: true
                            }
                        }

                        // 风险等级
                        RowLayout {
                            Layout.fillWidth: true
                            Text {
                                text: "等级:"
                                font.bold: true
                                Layout.minimumWidth: 60
                                Layout.maximumWidth: 80
                            }
                            Text {
                                text: riskDetailsDialog.riskDetails.riskLevel || "未知"
                                font.bold: true
                                Layout.fillWidth: true
                            }
                        }

                        // 敏感项数量
                        RowLayout {
                            Layout.fillWidth: true
                            Text {
                                text: "敏感项:"
                                font.bold: true
                                Layout.minimumWidth: 60
                                Layout.maximumWidth: 80
                            }
                            Text {
                                text: (riskDetailsDialog.riskDetails.sensitiveItemCount || 0).toString() + " 个"
                                Layout.fillWidth: true
                            }
                        }
                    }
                }
            }
        }

        standardButtons: Dialog.Close
    }

        contentItem: ScrollView {
            id: detailsScrollView
            anchors.fill: parent
            anchors.margins: 10
            clip: true

            ScrollBar.horizontal.policy: ScrollBar.AsNeeded
            ScrollBar.vertical.policy: ScrollBar.AsNeeded

            ColumnLayout {
                width: detailsScrollView.availableWidth
                spacing: 15

                // 文件基本信息
                GroupBox {
                    Layout.fillWidth: true
                    Layout.preferredHeight: implicitHeight
                    title: "文件信息"

                    ColumnLayout {
                        anchors.fill: parent
                        anchors.margins: 10
                        spacing: 8

                        // 文件名
                        RowLayout {
                            Layout.fillWidth: true
                            Text {
                                text: "文件名:"
                                font.bold: true
                                Layout.preferredWidth: 80
                            }
                            Text {
                                text: riskDetailsDialog.riskDetails.fileName || "未知"
                                Layout.fillWidth: true
                                wrapMode: Text.WordWrap
                                elide: Text.ElideRight
                            }
                        }

                        // 文件路径
                        RowLayout {
                            Layout.fillWidth: true
                            Text {
                                text: "路径:"
                                font.bold: true
                                Layout.preferredWidth: 80
                            }
                            Text {
                                text: riskDetailsDialog.riskDetails.filePath || "未知"
                                Layout.fillWidth: true
                                wrapMode: Text.WordWrap
                                maximumLineCount: 3
                                elide: Text.ElideRight
                            }
                        }

                        // 文件大小
                        RowLayout {
                            Layout.fillWidth: true
                            Text {
                                text: "大小:"
                                font.bold: true
                                Layout.preferredWidth: 80
                            }
                            Text {
                                text: (riskDetailsDialog.riskDetails.fileSize || 0) + " 字节"
                                Layout.fillWidth: true
                            }
                        }

                        // 最后修改时间
                        RowLayout {
                            Layout.fillWidth: true
                            Text {
                                text: "修改:"
                                font.bold: true
                                Layout.preferredWidth: 80
                            }
                            Text {
                                text: riskDetailsDialog.riskDetails.lastModified ?
                                      Qt.formatDateTime(riskDetailsDialog.riskDetails.lastModified, "yyyy-MM-dd hh:mm") : "未知"
                                Layout.fillWidth: true
                            }
                        }

                        // 数据类型
                        RowLayout {
                            Layout.fillWidth: true
                            Text {
                                text: "类型:"
                                font.bold: true
                                Layout.preferredWidth: 80
                            }
                            Text {
                                text: riskDetailsDialog.riskDetails.dataType || "未知"
                                Layout.fillWidth: true
                            }
                        }
                    }
                }

                // 风险评估信息
                GroupBox {
                    Layout.fillWidth: true
                    Layout.preferredHeight: implicitHeight
                    title: "风险评估"

                    ColumnLayout {
                        anchors.fill: parent
                        anchors.margins: 10
                        spacing: 8

                        // 风险评分
                        RowLayout {
                            Layout.fillWidth: true
                            Text {
                                text: "评分:"
                                font.bold: true
                                Layout.preferredWidth: 80
                            }
                            Text {
                                text: Math.round(riskDetailsDialog.riskDetails.riskScore || 0).toString()
                                font.bold: true
                                Layout.fillWidth: true
                            }
                        }

                        // 风险等级
                        RowLayout {
                            Layout.fillWidth: true
                            Text {
                                text: "等级:"
                                font.bold: true
                                Layout.preferredWidth: 80
                            }
                            Text {
                                text: riskDetailsDialog.riskDetails.riskLevel || "未知"
                                font.bold: true
                                Layout.fillWidth: true
                            }
                        }

                        // 敏感项数量
                        RowLayout {
                            Layout.fillWidth: true
                            Text {
                                text: "敏感项:"
                                font.bold: true
                                Layout.preferredWidth: 80
                            }
                            Text {
                                text: (riskDetailsDialog.riskDetails.sensitiveItemCount || 0).toString() + " 个"
                                Layout.fillWidth: true
                            }
                        }
                    }
                }
            }
        }

        standardButtons: Dialog.Close
    }

    // 缓解措施对话框
    Dialog {
        id: mitigationDialog
        title: "风险缓解措施"
        width: Math.min(650, parent.width * 0.9)
        height: Math.min(550, parent.height * 0.8)
        modal: true
        anchors.centerIn: parent

        property var measures: []
        property string riskLevel: ""

        contentItem: ScrollView {
            id: mitigationScrollView
            anchors.fill: parent
            anchors.margins: 10
            clip: true

            ScrollBar.horizontal.policy: ScrollBar.AsNeeded
            ScrollBar.vertical.policy: ScrollBar.AsNeeded

            ColumnLayout {
                width: mitigationScrollView.availableWidth
                spacing: 15

                // 风险等级信息
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: 60
                    Layout.maximumHeight: 80
                    radius: 8
                    border.width: 2
                    border.color: "#dee2e6"

                    ColumnLayout {
                        anchors.centerIn: parent
                        anchors.margins: 10
                        spacing: 5

                        Text {
                            text: "当前风险等级"
                            font.pixelSize: 14
                            horizontalAlignment: Text.AlignHCenter
                            Layout.alignment: Qt.AlignHCenter
                        }

                        Text {
                            text: mitigationDialog.riskLevel || "未知"
                            font.pixelSize: 18
                            font.bold: true
                            horizontalAlignment: Text.AlignHCenter
                            Layout.alignment: Qt.AlignHCenter
                        }
                    }
                }

                // 缓解措施列表
                GroupBox {
                    Layout.fillWidth: true
                    Layout.preferredHeight: implicitHeight
                    title: "建议的缓解措施"

                    ColumnLayout {
                        anchors.fill: parent
                        anchors.margins: 10
                        spacing: 10

                        Repeater {
                            model: mitigationDialog.measures || []
                            delegate: Rectangle {
                                Layout.fillWidth: true
                                Layout.preferredHeight: Math.max(40, measureText.implicitHeight + 20)
                                Layout.maximumHeight: 120
                                color: "#f8f9fa"
                                border.color: "#dee2e6"
                                border.width: 1
                                radius: 4

                                RowLayout {
                                    anchors.fill: parent
                                    anchors.margins: 10
                                    spacing: 10

                                    Rectangle {
                                        Layout.preferredWidth: 24
                                        Layout.preferredHeight: 24
                                        Layout.alignment: Qt.AlignTop
                                        radius: 12
                                        color: "#007bff"

                                        Text {
                                            anchors.centerIn: parent
                                            text: (index + 1).toString()
                                            color: "white"
                                            font.bold: true
                                            font.pixelSize: 12
                                        }
                                    }

                                    Text {
                                        id: measureText
                                        text: modelData
                                        Layout.fillWidth: true
                                        Layout.alignment: Qt.AlignVCenter
                                        wrapMode: Text.WordWrap
                                        maximumLineCount: 4
                                        elide: Text.ElideRight
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        standardButtons: Dialog.Close
    }

    // 自定义评分规则对话框
    Dialog {
        id: customRulesDialog
        title: "自定义评分规则"
        width: 700
        height: 600
        modal: true
        anchors.centerIn: parent

        ScrollView {
            anchors.fill: parent
            anchors.margins: 10

            ColumnLayout {
                width: parent.width
                spacing: 20

                // 数据类型权重设置
                GroupBox {
                    Layout.fillWidth: true
                    title: "数据类型风险权重"

                    GridLayout {
                        anchors.fill: parent
                        columns: 3
                        columnSpacing: 15
                        rowSpacing: 10

                        Text { text: "数据类型"; font.bold: true }
                        Text { text: "权重系数"; font.bold: true }
                        Text { text: "描述"; font.bold: true }

                        Text { text: "PII数据" }
                        RowLayout {
                            SpinBox {
                                from: 10
                                to: 50
                                value: 30
                            }
                            Text { text: "x" }
                        }
                        Text { text: "个人身份信息，高敏感度" }

                        Text { text: "财务数据" }
                        RowLayout {
                            SpinBox {
                                from: 10
                                to: 50
                                value: 40
                            }
                            Text { text: "x" }
                        }
                        Text { text: "财务相关信息，极高敏感度" }

                        Text { text: "健康数据" }
                        RowLayout {
                            SpinBox {
                                from: 10
                                to: 50
                                value: 45
                            }
                            Text { text: "x" }
                        }
                        Text { text: "医疗健康信息，极高敏感度" }

                        Text { text: "商业机密" }
                        RowLayout {
                            SpinBox {
                                from: 10
                                to: 50
                                value: 35
                            }
                            Text { text: "x" }
                        }
                        Text { text: "商业机密信息，高敏感度" }
                    }
                }

                // 风险阈值设置
                GroupBox {
                    Layout.fillWidth: true
                    title: "风险等级阈值"

                    GridLayout {
                        anchors.fill: parent
                        columns: 2
                        columnSpacing: 15
                        rowSpacing: 10

                        Text { text: "高风险阈值:"; font.bold: true }
                        RowLayout {
                            SpinBox {
                                from: 50
                                to: 100
                                value: 70
                            }
                            Text { text: " 分" }
                        }

                        Text { text: "中风险阈值:"; font.bold: true }
                        RowLayout {
                            SpinBox {
                                from: 20
                                to: 70
                                value: 40
                            }
                            Text { text: " 分" }
                        }

                        Text { text: "低风险阈值:"; font.bold: true }
                        RowLayout {
                            SpinBox {
                                from: 0
                                to: 40
                                value: 20
                            }
                            Text { text: " 分" }
                        }
                    }
                }

                // 文件类型权重
                GroupBox {
                    Layout.fillWidth: true
                    title: "文件类型权重"

                    GridLayout {
                        anchors.fill: parent
                        columns: 3
                        columnSpacing: 15
                        rowSpacing: 10

                        Text { text: "文件类型"; font.bold: true }
                        Text { text: "权重系数"; font.bold: true }
                        Text { text: "说明"; font.bold: true }

                        Text { text: "数据库文件 (.db, .sql)" }
                        RowLayout {
                            SpinBox {
                                from: 10
                                to: 30
                                value: 20
                            }
                            Text { text: "x" }
                        }
                        Text { text: "结构化数据存储" }

                        Text { text: "电子表格 (.xlsx, .csv)" }
                        RowLayout {
                            SpinBox {
                                from: 10
                                to: 20
                                value: 15
                            }
                            Text { text: "x" }
                        }
                        Text { text: "表格数据文件" }

                        Text { text: "文本文件 (.txt, .log)" }
                        RowLayout {
                            SpinBox {
                                from: 10
                                to: 15
                                value: 12
                            }
                            Text { text: "x" }
                        }
                        Text { text: "纯文本数据" }
                    }
                }
            }
        }

        standardButtons: Dialog.Save | Dialog.Cancel

        onAccepted: {
            console.log("[RiskAssessment] 保存自定义评分规则")
            // 这里可以实现保存规则的逻辑
        }
    }
}
